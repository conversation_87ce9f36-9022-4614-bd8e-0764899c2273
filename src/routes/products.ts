import { Router } from "express";

import {
  getAllProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductReviews,
  createProductReview,
  getProductByCategory,
} from "../controllers";

const router = Router();

router
  .get("/products", getAllProducts)
  .get("/products/:id", getProduct)
  .post("/products", createProduct)
  .patch("/products/:id", updateProduct)
  .delete("/products/:id", deleteProduct)
  .get("/products/:id/reviews", getProductReviews)
  .post("/products/:id/reviews", createProductReview)
  .get("/products/category/:id", getProductByCategory);

export { router as productsRouter };
