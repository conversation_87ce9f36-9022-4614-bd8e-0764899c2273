import multer from 'multer';
import path from 'path';
import fs from 'fs';
import type { Request } from 'express';
import { BadRequestError } from '../utils/errors.js';

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads');
const imagesDir = path.join(uploadsDir, 'images');
const documentsDir = path.join(uploadsDir, 'documents');

// Create directories if they don't exist
[uploadsDir, imagesDir, documentsDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Storage configuration
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    // Determine destination based on file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, imagesDir);
    } else {
      cb(null, documentsDir);
    }
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  }
});

// File filter function
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Define allowed file types
  const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const allowedDocumentTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  
  const allowedTypes = [...allowedImageTypes, ...allowedDocumentTypes];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new BadRequestError(`File type ${file.mimetype} is not allowed. Allowed types: ${allowedTypes.join(', ')}`));
  }
};

// Base multer configuration
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10, // Maximum 10 files
  },
});

// Middleware for single image upload
export const uploadSingleImage = upload.single('image');

// Middleware for multiple image uploads (for product galleries)
export const uploadMultipleImages = upload.array('images', 10);

// Middleware for mixed file uploads
export const uploadMixed = upload.fields([
  { name: 'images', maxCount: 10 },
  { name: 'documents', maxCount: 5 }
]);

// Middleware for any file upload
export const uploadAny = upload.any();

// Helper function to get file URL
export const getFileUrl = (req: Request, filename: string, type: 'images' | 'documents' = 'images'): string => {
  const protocol = req.protocol;
  const host = req.get('host');
  return `${protocol}://${host}/uploads/${type}/${filename}`;
};

// Helper function to process uploaded files and return URLs
export const processUploadedFiles = (req: Request, files: Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] }): string[] => {
  const fileUrls: string[] = [];
  
  if (Array.isArray(files)) {
    // Handle array of files (from uploadMultipleImages or uploadAny)
    files.forEach(file => {
      const type = file.mimetype.startsWith('image/') ? 'images' : 'documents';
      fileUrls.push(getFileUrl(req, file.filename, type));
    });
  } else if (files && typeof files === 'object') {
    // Handle object with field names (from uploadMixed)
    Object.values(files).forEach(fileArray => {
      fileArray.forEach(file => {
        const type = file.mimetype.startsWith('image/') ? 'images' : 'documents';
        fileUrls.push(getFileUrl(req, file.filename, type));
      });
    });
  }
  
  return fileUrls;
};

// Helper function to delete uploaded file
export const deleteUploadedFile = (filename: string, type: 'images' | 'documents' = 'images'): void => {
  const filePath = path.join(uploadsDir, type, filename);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }
};

// Helper function to extract filename from URL
export const getFilenameFromUrl = (url: string): string | null => {
  const match = url.match(/\/uploads\/(images|documents)\/(.+)$/);
  return match ? match[2] : null;
};

// Helper function to get file type from URL
export const getFileTypeFromUrl = (url: string): 'images' | 'documents' | null => {
  const match = url.match(/\/uploads\/(images|documents)\//);
  return match ? match[1] as 'images' | 'documents' : null;
};

// Middleware to validate uploaded images for products
export const validateProductImages = (req: Request, res: any, next: any) => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    return next(new BadRequestError('At least one product image is required'));
  }
  
  // Check if all files are images
  const nonImageFiles = files.filter(file => !file.mimetype.startsWith('image/'));
  if (nonImageFiles.length > 0) {
    return next(new BadRequestError('Only image files are allowed for product images'));
  }
  
  // Process files and add URLs to request body
  req.body.images = processUploadedFiles(req, files);
  
  next();
};

// Cleanup middleware to remove uploaded files on error
export const cleanupOnError = (req: Request, res: any, next: any) => {
  const originalSend = res.send;
  
  res.send = function(data: any) {
    // If there's an error status code, cleanup uploaded files
    if (res.statusCode >= 400 && req.files) {
      const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
      files.forEach((file: Express.Multer.File) => {
        const type = file.mimetype.startsWith('image/') ? 'images' : 'documents';
        deleteUploadedFile(file.filename, type);
      });
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};
