import type { Request, Response, NextFunction } from 'express';
import passport from 'passport';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import jwt from 'jsonwebtoken';
import { UnauthorizedError, ForbiddenError } from '../utils/errors.js';

// JWT Secret - in production, this should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// User interface for JWT payload
export interface JwtPayload {
  id: string;
  email: string;
  role?: string;
  iat?: number;
  exp?: number;
}

// Extend Express Request to include user
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

// Configure JWT Strategy
const jwtOptions = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: JWT_SECRET,
};

passport.use(
  new JwtStrategy(jwtOptions, async (payload: JwtPayload, done) => {
    try {
      // In a real app, you would verify the user exists in the database
      // For now, we'll just return the payload
      return done(null, payload);
    } catch (error) {
      return done(error, false);
    }
  })
);

// Authentication middleware
export const authenticate = (req: Request, res: Response, next: NextFunction) => {
  passport.authenticate('jwt', { session: false }, (err: any, user: JwtPayload) => {
    if (err) {
      return next(new UnauthorizedError('Authentication failed'));
    }
    
    if (!user) {
      return next(new UnauthorizedError('Invalid or expired token'));
    }
    
    req.user = user;
    next();
  })(req, res, next);
};

// Optional authentication middleware (doesn't throw error if no token)
export const optionalAuth = (req: Request, res: Response, next: NextFunction) => {
  const token = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
  
  if (!token) {
    return next();
  }
  
  passport.authenticate('jwt', { session: false }, (err: any, user: JwtPayload) => {
    if (!err && user) {
      req.user = user;
    }
    next();
  })(req, res, next);
};

// Role-based authorization middleware
export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }
    
    if (roles.length && !roles.includes(req.user.role || '')) {
      return next(new ForbiddenError('Insufficient permissions'));
    }
    
    next();
  };
};

// JWT token generation utility
export const generateToken = (payload: Omit<JwtPayload, 'iat' | 'exp'>): string => {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
};

// JWT token verification utility
export const verifyToken = (token: string): JwtPayload => {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    throw new UnauthorizedError('Invalid token');
  }
};

// Middleware to check if user owns resource
export const checkOwnership = (userIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }
    
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (req.user.id !== resourceUserId && req.user.role !== 'admin') {
      return next(new ForbiddenError('Access denied'));
    }
    
    next();
  };
};
