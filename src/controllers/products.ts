import type { Request, Response } from "express";
import Handler from "express-async-handler";

export const getAllProducts = Handler(
  async (req: Request, res: Response) => {}
);

export const getProduct = Handler(async (req: Request, res: Response) => {});

export const createProduct = Handler(async (req: Request, res: Response) => {});

export const updateProduct = Handler(async (req: Request, res: Response) => {});

export const deleteProduct = Handler(async (req: Request, res: Response) => {});

export const getProductReviews = Handler(
  async (req: Request, res: Response) => {}
);

export const createProductReview = Handler(
  async (req: Request, res: Response) => {}
);

export const getProductByCategory = Handler(
  async (req: Request, res: Response) => {}
);
