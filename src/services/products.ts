import type {
  Product,
  CreateProductDto,
  UpdateProductDto,
  ProductQueryDto,
  ProductReview,
  CreateProductReviewDto,
  UpdateProductReviewDto,
  ProductListResponse,
  ProductWithReviews,
} from "../dto/products.js";
import {
  NotFoundError,
  ConflictError,
  ForbiddenError,
} from "../utils/errors.js";

// Mock database - in a real app, this would be replaced with actual database operations
let products: Product[] = [];
let reviews: ProductReview[] = [];

export class ProductService {
  // Get all products with filtering and pagination
  static async getAllProducts(
    query: ProductQueryDto
  ): Promise<ProductListResponse> {
    let filteredProducts = [...products];

    // Apply filters
    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.name.toLowerCase().includes(searchTerm) ||
          product.description.toLowerCase().includes(searchTerm) ||
          product.brand.toLowerCase().includes(searchTerm) ||
          product.model.toLowerCase().includes(searchTerm)
      );
    }

    if (query.categoryId) {
      filteredProducts = filteredProducts.filter(
        (product) => product.categoryId === query.categoryId
      );
    }

    if (query.brand) {
      filteredProducts = filteredProducts.filter(
        (product) => product.brand.toLowerCase() === query.brand!.toLowerCase()
      );
    }

    if (query.condition) {
      filteredProducts = filteredProducts.filter(
        (product) => product.condition === query.condition
      );
    }

    if (query.fuelType) {
      filteredProducts = filteredProducts.filter(
        (product) => product.fuelType === query.fuelType
      );
    }

    if (query.transmission) {
      filteredProducts = filteredProducts.filter(
        (product) => product.transmission === query.transmission
      );
    }

    if (query.minPrice) {
      filteredProducts = filteredProducts.filter(
        (product) => product.price >= query.minPrice!
      );
    }

    if (query.maxPrice) {
      filteredProducts = filteredProducts.filter(
        (product) => product.price <= query.maxPrice!
      );
    }

    if (query.minYear) {
      filteredProducts = filteredProducts.filter(
        (product) => product.year >= query.minYear!
      );
    }

    if (query.maxYear) {
      filteredProducts = filteredProducts.filter(
        (product) => product.year <= query.maxYear!
      );
    }

    if (query.location) {
      filteredProducts = filteredProducts.filter((product) =>
        product.location.toLowerCase().includes(query.location!.toLowerCase())
      );
    }

    // Apply sorting
    filteredProducts.sort((a, b) => {
      const aValue = a[query.sortBy as keyof Product];
      const bValue = b[query.sortBy as keyof Product];

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return query.sortOrder === "asc" ? 1 : -1;
      if (bValue === undefined) return query.sortOrder === "asc" ? -1 : 1;

      if (query.sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const total = filteredProducts.length;
    const totalPages = Math.ceil(total / query.limit);
    const startIndex = (query.page - 1) * query.limit;
    const endIndex = startIndex + query.limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    return {
      products: paginatedProducts,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    };
  }

  // Get single product by ID
  static async getProductById(id: string): Promise<Product> {
    const product = products.find((p) => p.id === id);
    if (!product) {
      throw new NotFoundError("Product not found");
    }
    return product;
  }

  // Create new product
  static async createProduct(
    data: CreateProductDto,
    sellerId: string
  ): Promise<Product> {
    const newProduct: Product = {
      id: crypto.randomUUID(),
      ...data,
      sellerId,
      status: "active",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    products.push(newProduct);
    return newProduct;
  }

  // Update product
  static async updateProduct(
    id: string,
    data: UpdateProductDto,
    userId: string,
    userRole?: string
  ): Promise<Product> {
    const productIndex = products.findIndex((p) => p.id === id);
    if (productIndex === -1) {
      throw new NotFoundError("Product not found");
    }

    const product = products[productIndex];
    if (!product) {
      throw new NotFoundError("Product not found");
    }

    // Check ownership (only owner or admin can update)
    if (product.sellerId !== userId && userRole !== "admin") {
      throw new ForbiddenError("You can only update your own products");
    }

    const updatedProduct: Product = {
      ...product,
      ...Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      ),
      updatedAt: new Date(),
    } as Product;

    products[productIndex] = updatedProduct;
    return updatedProduct;
  }

  // Delete product
  static async deleteProduct(
    id: string,
    userId: string,
    userRole?: string
  ): Promise<void> {
    const productIndex = products.findIndex((p) => p.id === id);
    if (productIndex === -1) {
      throw new NotFoundError("Product not found");
    }

    const product = products[productIndex];
    if (!product) {
      throw new NotFoundError("Product not found");
    }

    // Check ownership (only owner or admin can delete)
    if (product.sellerId !== userId && userRole !== "admin") {
      throw new ForbiddenError("You can only delete your own products");
    }

    products.splice(productIndex, 1);
    // Also remove associated reviews
    reviews = reviews.filter((r) => r.productId !== id);
  }

  // Get products by category
  static async getProductsByCategory(
    categoryId: string,
    query: Omit<ProductQueryDto, "categoryId">
  ): Promise<ProductListResponse> {
    return this.getAllProducts({ ...query, categoryId });
  }

  // Get product reviews
  static async getProductReviews(
    productId: string
  ): Promise<ProductWithReviews> {
    const product = await this.getProductById(productId);
    const productReviews = reviews.filter((r) => r.productId === productId);

    const averageRating =
      productReviews.length > 0
        ? productReviews.reduce((sum, review) => sum + review.rating, 0) /
          productReviews.length
        : 0;

    return {
      ...product,
      reviews: productReviews,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      reviewCount: productReviews.length,
    };
  }

  // Create product review
  static async createProductReview(
    productId: string,
    data: CreateProductReviewDto,
    userId: string
  ): Promise<ProductReview> {
    // Check if product exists
    await this.getProductById(productId);

    // Check if user already reviewed this product
    const existingReview = reviews.find(
      (r) => r.productId === productId && r.userId === userId
    );
    if (existingReview) {
      throw new ConflictError("You have already reviewed this product");
    }

    const newReview: ProductReview = {
      id: crypto.randomUUID(),
      productId,
      userId,
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    reviews.push(newReview);
    return newReview;
  }

  // Update product review
  static async updateProductReview(
    reviewId: string,
    data: UpdateProductReviewDto,
    userId: string,
    userRole?: string
  ): Promise<ProductReview> {
    const reviewIndex = reviews.findIndex((r) => r.id === reviewId);
    if (reviewIndex === -1) {
      throw new NotFoundError("Review not found");
    }

    const review = reviews[reviewIndex];
    if (!review) {
      throw new NotFoundError("Review not found");
    }

    // Check ownership (only reviewer or admin can update)
    if (review.userId !== userId && userRole !== "admin") {
      throw new ForbiddenError("You can only update your own reviews");
    }

    const updatedReview: ProductReview = {
      ...review,
      ...Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      ),
      updatedAt: new Date(),
    } as ProductReview;

    reviews[reviewIndex] = updatedReview;
    return updatedReview;
  }

  // Delete product review
  static async deleteProductReview(
    reviewId: string,
    userId: string,
    userRole?: string
  ): Promise<void> {
    const reviewIndex = reviews.findIndex((r) => r.id === reviewId);
    if (reviewIndex === -1) {
      throw new NotFoundError("Review not found");
    }

    const review = reviews[reviewIndex];
    if (!review) {
      throw new NotFoundError("Review not found");
    }

    // Check ownership (only reviewer or admin can delete)
    if (review.userId !== userId && userRole !== "admin") {
      throw new ForbiddenError("You can only delete your own reviews");
    }

    reviews.splice(reviewIndex, 1);
  }
}
